/* أنماط الطباعة الاحترافية */

@media print {
    /* إعدادات عامة للطباعة الرسمية الاحترافية */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    @page {
        size: A4;
        margin: 2cm 1.5cm;
        direction: rtl;
    }

    body {
        font-family: 'Noto Sans Arabic', '<PERSON><PERSON>', 'Traditional Arabic', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
        font-size: 11pt;
        line-height: 1.7;
        color: #1a1a1a;
        background: white;
        direction: rtl;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* تحسين جودة الطباعة والخطوط العربية */
    body.print-professional {
        font-weight: 400;
        letter-spacing: 0.4px;
        word-spacing: 1px;
    }

    /* تحسين العناوين العربية */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Noto Sans Arabic', '<PERSON><PERSON>', 'Traditional Arabic', 'Tahoma', Arial, sans-serif !important;
        font-weight: bold !important;
        line-height: 1.4 !important;
        letter-spacing: 0.5px !important;
        word-spacing: 1.5px !important;
    }

    /* تحسين النصوص العربية في الجداول */
    .data-table th,
    .data-table td {
        font-family: 'Noto Sans Arabic', 'Tahoma', Arial, sans-serif !important;
        line-height: 1.6 !important;
        letter-spacing: 0.3px !important;
    }

    /* تحسين النصوص في الرأس والتذييل */
    .official-print-header *,
    .official-print-footer * {
        font-family: 'Noto Sans Arabic', 'Amiri', 'Traditional Arabic', 'Tahoma', Arial, sans-serif !important;
        text-rendering: optimizeLegibility !important;
    }

    /* إخفاء العناصر غير المطلوبة في الطباعة */
    .main-nav,
    .section-actions,
    .filters-section,
    .action-buttons,
    .btn,
    .modal-overlay,
    .app-footer,
    .no-print {
        display: none !important;
    }

    /* الرأس الرسمي الاحترافي */
    .official-print-header {
        margin-bottom: 30px !important;
        page-break-inside: avoid !important;
    }

    .ministry-header {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 20px 0 !important;
        border-bottom: 3px solid #2c5aa0 !important;
        margin-bottom: 20px !important;
    }

    .ministry-logo {
        flex: 0 0 80px !important;
    }

    .logo-circle {
        width: 80px !important;
        height: 80px !important;
        border-radius: 50% !important;
        background: linear-gradient(135deg, #2c5aa0, #1e3a8a) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 32px !important;
        box-shadow: 0 4px 8px rgba(44, 90, 160, 0.3) !important;
    }

    .ministry-info {
        flex: 1 !important;
        text-align: center !important;
        padding: 0 20px !important;
    }

    .ministry-name {
        font-size: 18pt !important;
        font-weight: bold !important;
        color: #2c5aa0 !important;
        margin: 0 0 5px 0 !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
    }

    .ministry-dept {
        font-size: 16pt !important;
        font-weight: 600 !important;
        color: #1e3a8a !important;
        margin: 0 0 8px 0 !important;
    }

    .school-name {
        font-size: 14pt !important;
        font-weight: 500 !important;
        color: #374151 !important;
        margin: 0 !important;
        padding: 8px 16px !important;
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb) !important;
        border-radius: 8px !important;
        border: 1px solid #d1d5db !important;
    }

    .document-info {
        flex: 0 0 200px !important;
        text-align: left !important;
        font-size: 10pt !important;
        color: #4b5563 !important;
        line-height: 1.8 !important;
    }

    .doc-date, .doc-time, .academic-year {
        display: block !important;
        margin-bottom: 4px !important;
        font-weight: 500 !important;
    }

    .document-title {
        text-align: center !important;
        margin-bottom: 25px !important;
    }

    .main-title {
        font-size: 20pt !important;
        font-weight: bold !important;
        color: #1e3a8a !important;
        margin: 0 0 10px 0 !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
    }

    .title-decoration {
        width: 200px !important;
        height: 3px !important;
        background: linear-gradient(90deg, #2c5aa0, #1e3a8a, #2c5aa0) !important;
        margin: 10px auto !important;
        border-radius: 2px !important;
    }

    .document-subtitle {
        font-size: 12pt !important;
        color: #6b7280 !important;
        font-style: italic !important;
        margin: 10px 0 0 0 !important;
    }

    /* رأس التقرير المحسن */
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 30px;
        padding: 25px 20px;
        border: 3px double #2c3e50;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        page-break-inside: avoid;
    }

    .print-header .school-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 15px;
        background: #2c3e50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24pt;
    }

    .print-header h1 {
        font-size: 18pt;
        font-weight: bold;
        color: #2c3e50;
        margin: 10px 0;
    }

    .print-header .school-info {
        font-size: 14pt;
        color: #555;
        margin: 5px 0;
    }

    .print-header .report-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-top: 15px;
        font-size: 11pt;
        color: #2c3e50;
        font-weight: 500;
    }

    .print-header .report-subtitle {
        margin-top: 15px;
        font-size: 12pt;
        color: #34495e;
        font-weight: 500;
        font-style: italic;
        border-top: 1px solid #bdc3c7;
        padding-top: 10px;
    }

    /* عنوان التقرير */
    .report-title {
        text-align: center;
        margin: 20px 0;
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px solid #2c3e50;
        border-radius: 8px;
    }

    .report-title h2 {
        font-size: 16pt;
        font-weight: bold;
        color: #2c3e50;
        margin: 0;
    }

    .report-title .report-details {
        font-size: 12pt;
        color: #555;
        margin-top: 8px;
    }

    /* جداول البيانات الاحترافية */
    .data-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 25px 0 !important;
        font-size: 10pt !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        border-radius: 8px !important;
        overflow: hidden !important;
    }

    .data-table th {
        background: linear-gradient(135deg, #2c5aa0, #1e3a8a) !important;
        color: white !important;
        font-weight: bold !important;
        padding: 14px 10px !important;
        text-align: center !important;
        border: 1px solid #1e3a8a !important;
        font-size: 10pt !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
        letter-spacing: 0.5px !important;
    }

    .data-table td {
        padding: 12px 8px !important;
        text-align: center !important;
        border: 1px solid #d1d5db !important;
        vertical-align: middle !important;
        line-height: 1.5 !important;
    }

    /* تلوين الصفوف بالتناوب مع تدرج احترافي */
    .data-table tbody tr:nth-child(even) {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
    }

    .data-table tbody tr:nth-child(odd) {
        background: white !important;
    }

    /* تحسين خلايا البيانات المهمة */
    .data-table .rank-cell {
        background: linear-gradient(135deg, #fef3c7, #fde68a) !important;
        font-weight: bold !important;
        color: #92400e !important;
        border: 2px solid #f59e0b !important;
    }

    .data-table .student-info {
        font-weight: 600 !important;
        color: #1f2937 !important;
        text-align: right !important;
        padding-right: 12px !important;
        background: linear-gradient(135deg, #f9fafb, #f3f4f6) !important;
    }

    .data-table .final-total {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
        font-weight: bold !important;
        color: #1e40af !important;
        border: 2px solid #3b82f6 !important;
    }

    .data-table .average-cell {
        background: linear-gradient(135deg, #d1fae5, #a7f3d0) !important;
        font-weight: bold !important;
        color: #065f46 !important;
        border: 2px solid #10b981 !important;
    }

    .data-table .percentage-cell {
        background: linear-gradient(135deg, #fce7f3, #fbcfe8) !important;
        font-weight: bold !important;
        color: #be185d !important;
        border: 2px solid #ec4899 !important;
    }

    .data-table .grade-cell {
        font-weight: bold !important;
        font-size: 11pt !important;
        border: 2px solid #6b7280 !important;
    }

    /* تلوين التقديرات بتدرجات احترافية */
    .grade-excellent {
        background: linear-gradient(135deg, #dcfce7, #bbf7d0) !important;
        color: #166534 !important;
        font-weight: bold !important;
        border: 2px solid #22c55e !important;
        text-shadow: 1px 1px 2px rgba(22, 101, 52, 0.2) !important;
    }

    .grade-very-good {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
        color: #1e40af !important;
        font-weight: bold !important;
        border: 2px solid #3b82f6 !important;
        text-shadow: 1px 1px 2px rgba(30, 64, 175, 0.2) !important;
    }

    .grade-good {
        background: linear-gradient(135deg, #fef3c7, #fde68a) !important;
        color: #92400e !important;
        font-weight: bold !important;
        border: 2px solid #f59e0b !important;
        text-shadow: 1px 1px 2px rgba(146, 64, 14, 0.2) !important;
    }

    .grade-acceptable {
        background: linear-gradient(135deg, #fed7aa, #fdba74) !important;
        color: #c2410c !important;
        font-weight: bold !important;
        border: 2px solid #f97316 !important;
        text-shadow: 1px 1px 2px rgba(194, 65, 12, 0.2) !important;
    }

    .grade-weak {
        background: linear-gradient(135deg, #fecaca, #fca5a5) !important;
        color: #dc2626 !important;
        font-weight: bold !important;
        border: 2px solid #ef4444 !important;
        text-shadow: 1px 1px 2px rgba(220, 38, 38, 0.2) !important;
    }

    /* الإحصائيات */
    .statistics-grid {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin: 20px 0;
    }

    .stat-card {
        border: 2px solid #2c3e50;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        background: #f8f9fa !important;
    }

    .stat-card h4 {
        font-size: 12pt;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .stat-number {
        font-size: 18pt;
        font-weight: bold;
        color: #e74c3c;
    }

    .stat-percentage {
        font-size: 10pt;
        color: #7f8c8d;
        margin-top: 5px;
    }

    /* الرسوم البيانية للطباعة */
    .chart-container {
        page-break-inside: avoid;
        margin: 20px 0;
        text-align: center;
    }

    .chart-title {
        font-size: 14pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 15px;
    }

    /* تذييل التقرير المحسن */
    .print-footer {
        display: block !important;
        margin-top: 40px;
        padding: 25px 20px;
        border: 3px double #2c3e50;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        page-break-inside: avoid;
    }

    .footer-info {
        margin-bottom: 25px;
        padding: 15px;
        background: white !important;
        border: 1px solid #bdc3c7;
        border-radius: 5px;
    }

    .report-summary-footer p {
        font-size: 11pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .report-summary-footer ul {
        margin: 0;
        padding-right: 20px;
        font-size: 10pt;
        color: #555;
    }

    .report-summary-footer li {
        margin-bottom: 5px;
        line-height: 1.4;
    }

    .signature-section {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        margin-top: 30px;
    }

    .signature-box {
        text-align: center;
        padding: 20px 10px;
    }

    .signature-line {
        border-bottom: 3px solid #2c3e50;
        margin-bottom: 12px;
        height: 50px;
        background: linear-gradient(to bottom, transparent 80%, #f8f9fa 80%);
    }

    .signature-label {
        font-size: 12pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .signature-title {
        font-size: 10pt;
        color: #555;
        margin-top: 5px;
        font-weight: 500;
    }

    .signature-date {
        font-size: 9pt;
        color: #777;
        margin-top: 8px;
        font-style: italic;
        border: 1px dashed #bdc3c7;
        padding: 3px 8px;
        border-radius: 3px;
        background: #f8f9fa !important;
    }

    /* ختم المدرسة */
    .footer-stamp {
        text-align: center;
        margin-top: 30px;
    }

    .official-stamp {
        display: inline-block;
        margin: 0 auto;
    }

    .stamp-border {
        width: 120px;
        height: 120px;
        border: 3px solid #2c3e50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: radial-gradient(circle, #f8f9fa, #e9ecef) !important;
        position: relative;
    }

    .stamp-border::before {
        content: "";
        position: absolute;
        width: 100px;
        height: 100px;
        border: 2px dashed #34495e;
        border-radius: 50%;
    }

    .stamp-content {
        text-align: center;
        z-index: 1;
    }

    .stamp-text {
        font-size: 9pt;
        font-weight: bold;
        color: #2c3e50;
        line-height: 1.2;
    }

    .stamp-date {
        font-size: 8pt;
        color: #555;
        margin-top: 5px;
    }

    /* معلومات الطباعة */
    .print-info {
        position: fixed;
        bottom: 1cm;
        left: 1cm;
        right: 1cm;
        font-size: 9pt;
        color: #7f8c8d;
        text-align: center;
        border-top: 1px solid #bdc3c7;
        padding-top: 10px;
    }

    /* فواصل الصفحات */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* تنسيق خاص للعناوين */
    h1, h2, h3 {
        color: #2c3e50 !important;
        page-break-after: avoid;
    }

    h1 {
        font-size: 18pt;
        margin: 20px 0 15px;
    }

    h2 {
        font-size: 16pt;
        margin: 18px 0 12px;
    }

    h3 {
        font-size: 14pt;
        margin: 15px 0 10px;
    }

    /* تنسيق الفقرات */
    p {
        margin: 8px 0;
        text-align: justify;
    }

    /* تنسيق القوائم */
    ul, ol {
        margin: 10px 0;
        padding-right: 20px;
    }

    li {
        margin: 5px 0;
    }

    /* تنسيق خاص للدرجات */
    .grade-cell {
        font-weight: bold;
        font-size: 11pt;
    }

    .total-grade {
        background: #2c3e50 !important;
        color: white !important;
        font-weight: bold;
        font-size: 12pt;
    }

    /* تنسيق التقديرات */
    .grade-a { color: #27ae60 !important; font-weight: bold; }
    .grade-b { color: #3498db !important; font-weight: bold; }
    .grade-c { color: #f39c12 !important; font-weight: bold; }
    .grade-d { color: #e67e22 !important; font-weight: bold; }
    .grade-f { color: #e74c3c !important; font-weight: bold; }

    /* تنسيق خاص للملاحظات */
    .notes-section {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #bdc3c7;
        border-radius: 5px;
        background: #f8f9fa !important;
    }

    .notes-title {
        font-size: 12pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .notes-content {
        font-size: 10pt;
        line-height: 1.6;
        color: #555;
    }

    /* تنسيق التواريخ والأرقام */
    .date, .number {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    /* تحسينات للطباعة الملونة */
    .color-print .grade-excellent { background: #d4edda !important; }
    .color-print .grade-very-good { background: #d1ecf1 !important; }
    .color-print .grade-good { background: #fff3cd !important; }
    .color-print .grade-acceptable { background: #ffeaa7 !important; }
    .color-print .grade-weak { background: #f8d7da !important; }

    /* تحسينات للطباعة بالأبيض والأسود */
    .bw-print .grade-excellent { background: #f0f0f0 !important; border: 2px solid #000; }
    .bw-print .grade-very-good { background: #e8e8e8 !important; border: 1px solid #000; }
    .bw-print .grade-good { background: #e0e0e0 !important; }
    .bw-print .grade-acceptable { background: #d8d8d8 !important; }
    .bw-print .grade-weak { background: #d0d0d0 !important; text-decoration: underline; }

    /* طباعة النتائج النهائية */
    .printing-final-results .final-results-filters,
    .printing-final-results .section-actions,
    .printing-final-results .main-nav,
    .printing-final-results .app-footer {
        display: none !important;
    }

    .printing-final-results .final-results-container {
        box-shadow: none !important;
        border: none !important;
    }

    .printing-final-results .final-results-header {
        background: #2c3e50 !important;
        color: white !important;
        text-align: center;
        padding: 20px;
        margin-bottom: 20px;
    }

    .printing-final-results .final-results-table {
        font-size: 9pt;
        border-collapse: collapse;
    }

    .printing-final-results .final-results-table th,
    .printing-final-results .final-results-table td {
        border: 1px solid #000 !important;
        padding: 6px 4px;
        text-align: center;
    }

    .printing-final-results .final-results-table th {
        background: #2c3e50 !important;
        color: white !important;
        font-weight: bold;
    }

    .printing-final-results .final-grade-a {
        background: #c8e6c9 !important;
        color: #2e7d32 !important;
    }

    .printing-final-results .final-grade-b {
        background: #bbdefb !important;
        color: #1565c0 !important;
    }

    .printing-final-results .final-grade-c {
        background: #fff3e0 !important;
        color: #ef6c00 !important;
    }

    .printing-final-results .final-grade-d {
        background: #ffecb3 !important;
        color: #f57f17 !important;
    }

    .printing-final-results .final-grade-f {
        background: #ffcdd2 !important;
        color: #c62828 !important;
    }

    .printing-final-results .final-total {
        background: #2c3e50 !important;
        color: white !important;
        font-weight: bold;
    }

    .printing-final-results .rank-cell,
    .printing-final-results .average-cell,
    .printing-final-results .percentage-cell,
    .printing-final-results .grade-cell {
        font-weight: bold;
    }

    .printing-final-results .final-results-summary {
        page-break-before: avoid;
        margin-top: 20px;
        padding: 15px;
        border: 2px solid #2c3e50;
        border-radius: 5px;
    }

    .printing-final-results .summary-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        margin-bottom: 15px;
    }

    .printing-final-results .summary-stat {
        text-align: center;
        padding: 10px;
        border: 1px solid #bdc3c7;
        border-radius: 3px;
    }

    .printing-final-results .grade-distribution-summary {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .printing-final-results .grade-summary-item {
        padding: 8px 12px;
        border-radius: 3px;
        font-weight: bold;
        text-align: center;
        min-width: 70px;
    }

    .printing-final-results .incomplete-data {
        background: #fff3cd !important;
        border-left: 4px solid #ffc107 !important;
    }

    /* أنماط الطباعة المتقدمة */

    /* الطباعة الملونة عالية الجودة */
    .print-color .print-header {
        background: linear-gradient(135deg, #2c3e50, #3498db) !important;
        color: white !important;
        border: 3px solid #34495e !important;
    }

    .print-color .data-table th {
        background: linear-gradient(135deg, #2c3e50, #34495e) !important;
        color: white !important;
        border: 2px solid #2c3e50 !important;
    }

    .print-color .grade-excellent {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        border: 2px solid #28a745 !important;
    }

    .print-color .grade-very-good {
        background: linear-gradient(135deg, #d1ecf1, #bee5eb) !important;
        border: 2px solid #17a2b8 !important;
    }

    .print-color .grade-good {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        border: 2px solid #ffc107 !important;
    }

    .print-color .grade-acceptable {
        background: linear-gradient(135deg, #ffeaa7, #fdcb6e) !important;
        border: 2px solid #fd7e14 !important;
    }

    .print-color .grade-weak {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb) !important;
        border: 2px solid #dc3545 !important;
    }

    /* الطباعة بالأبيض والأسود */
    .print-bw * {
        background: white !important;
        color: black !important;
        border-color: black !important;
    }

    .print-bw .print-header {
        background: white !important;
        color: black !important;
        border: 3px solid black !important;
    }

    .print-bw .data-table th {
        background: #f0f0f0 !important;
        color: black !important;
        border: 2px solid black !important;
        font-weight: bold !important;
    }

    .print-bw .grade-excellent {
        background: #f8f8f8 !important;
        border: 3px solid black !important;
        font-weight: bold !important;
    }

    .print-bw .grade-very-good {
        background: #f0f0f0 !important;
        border: 2px solid black !important;
        font-weight: bold !important;
    }

    .print-bw .grade-good {
        background: #e8e8e8 !important;
        border: 1px solid black !important;
    }

    .print-bw .grade-acceptable {
        background: #e0e0e0 !important;
        border: 1px dashed black !important;
    }

    .print-bw .grade-weak {
        background: #d8d8d8 !important;
        border: 1px dotted black !important;
        text-decoration: underline !important;
    }

    /* الطباعة الرسمية */
    .print-official .print-header {
        background: #2c3e50 !important;
        color: white !important;
        border: 4px double #34495e !important;
        padding: 30px !important;
    }

    .print-official .print-header .school-logo {
        width: 100px !important;
        height: 100px !important;
        border: 3px solid white !important;
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
    }

    .print-official .print-header h1 {
        font-size: 24pt !important;
        font-weight: bold !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
        margin: 15px 0 !important;
    }

    .print-official .data-table {
        border: 3px solid #2c3e50 !important;
        border-radius: 8px !important;
    }

    .print-official .data-table th {
        background: linear-gradient(135deg, #2c3e50, #34495e) !important;
        color: white !important;
        font-size: 12pt !important;
        font-weight: bold !important;
        padding: 15px 10px !important;
        border: 2px solid #2c3e50 !important;
    }

    .print-official .data-table td {
        padding: 12px 8px !important;
        border: 1px solid #2c3e50 !important;
        font-size: 11pt !important;
    }

    .print-official .signature-section {
        border: 3px double #2c3e50 !important;
        border-radius: 10px !important;
        padding: 30px !important;
        margin-top: 40px !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    }

    .print-official .signature-line {
        border-bottom: 3px solid #2c3e50 !important;
        height: 50px !important;
    }

    .print-official .signature-label {
        font-size: 14pt !important;
        font-weight: bold !important;
        color: #2c3e50 !important;
    }

    /* الطباعة الملخص التنفيذي */
    .print-summary .detailed-grades-section {
        display: none !important;
    }

    .print-summary .statistics-section,
    .print-summary .grade-distribution-section {
        page-break-inside: avoid !important;
        border: 2px solid #2c3e50 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        margin: 20px 0 !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    }

    .print-summary .stat-card {
        border: 2px solid #3498db !important;
        border-radius: 8px !important;
        padding: 20px !important;
        background: white !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
    }

    .print-summary .stat-number {
        font-size: 24pt !important;
        font-weight: bold !important;
        color: #2c3e50 !important;
    }

    /* تحسينات إضافية للطباعة */
    .print-metadata {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
        border: 2px solid #2196f3 !important;
        border-radius: 8px !important;
        padding: 15px !important;
        margin-bottom: 20px !important;
        text-align: center !important;
    }

    .print-info-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        font-size: 10pt !important;
        color: #2c3e50 !important;
        font-weight: bold !important;
        margin-bottom: 15px !important;
    }

    /* تنسيق أسماء المسؤولين في الطباعة */
    .print-officials {
        margin-top: 15px !important;
    }

    .print-school-info {
        text-align: center !important;
        font-size: 14pt !important;
        font-weight: bold !important;
        color: #2c3e50 !important;
        margin-bottom: 15px !important;
        border-bottom: 2px solid #2196f3 !important;
        padding-bottom: 10px !important;
    }

    .print-names-row {
        display: flex !important;
        justify-content: space-between !important;
        align-items: flex-start !important;
        gap: 20px !important;
        margin-top: 15px !important;
    }

    .print-official {
        flex: 1 !important;
        text-align: center !important;
        padding: 10px !important;
        border: 1px solid #ddd !important;
        border-radius: 5px !important;
        background: rgba(255, 255, 255, 0.8) !important;
    }

    .official-title {
        display: block !important;
        font-weight: bold !important;
        font-size: 11pt !important;
        color: #2c3e50 !important;
        margin-bottom: 5px !important;
    }

    .official-name {
        display: block !important;
        font-size: 12pt !important;
        color: #1976d2 !important;
        font-weight: bold !important;
        margin-bottom: 10px !important;
        min-height: 20px !important;
    }

    .signature-line {
        display: block !important;
        font-size: 9pt !important;
        color: #666 !important;
        margin-top: 10px !important;
    }

    /* التذييل الرسمي الاحترافي المضغوط */
    .official-print-footer {
        margin-top: 25px !important;
        page-break-inside: avoid !important;
        border-top: 2px solid #2c5aa0 !important;
        padding-top: 15px !important;
    }

    /* التوقيعات المضغوطة في سطر واحد */
    .signatures-section-compact {
        margin-bottom: 15px !important;
        padding: 15px !important;
        background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
        border: 1px solid #d1d5db !important;
        border-radius: 8px !important;
    }

    .signatures-inline {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 20px !important;
        flex-wrap: wrap !important;
    }

    .signature-inline {
        flex: 1 !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        min-width: 250px !important;
        font-size: 9pt !important;
    }

    .sig-title {
        font-weight: bold !important;
        color: #1e3a8a !important;
        white-space: nowrap !important;
    }

    .sig-name {
        font-weight: 600 !important;
        color: #374151 !important;
        white-space: nowrap !important;
        min-width: 80px !important;
    }

    .sig-line {
        color: #6b7280 !important;
        font-size: 8pt !important;
        white-space: nowrap !important;
    }

    /* التوقيعات القديمة (للتوافق مع الأكواد الأخرى) */
    .signatures-section {
        margin-bottom: 30px !important;
    }

    .signature-row {
        display: flex !important;
        justify-content: space-between !important;
        align-items: flex-start !important;
        gap: 20px !important;
    }

    .signature-box {
        flex: 1 !important;
        text-align: center !important;
        padding: 20px 15px !important;
        border: 2px solid #2c5aa0 !important;
        border-radius: 10px !important;
        background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
        min-height: 120px !important;
    }

    .signature-title {
        font-size: 12pt !important;
        font-weight: bold !important;
        color: #1e3a8a !important;
        margin-bottom: 8px !important;
        text-decoration: underline !important;
    }

    .signature-name {
        font-size: 11pt !important;
        color: #374151 !important;
        font-weight: 600 !important;
        margin-bottom: 15px !important;
        min-height: 20px !important;
    }

    .signature-line {
        width: 80% !important;
        height: 2px !important;
        background: #6b7280 !important;
        margin: 15px auto !important;
        border-radius: 1px !important;
    }

    .signature-label {
        font-size: 9pt !important;
        color: #6b7280 !important;
        margin-bottom: 8px !important;
        font-style: italic !important;
    }

    .signature-date {
        font-size: 9pt !important;
        color: #9ca3af !important;
        font-style: italic !important;
    }

    .document-footer {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 20px 0 !important;
        border-top: 1px solid #d1d5db !important;
        margin-top: 20px !important;
    }

    .footer-note {
        flex: 1 !important;
    }

    .footer-note p {
        margin: 0 0 5px 0 !important;
        font-size: 9pt !important;
        color: #6b7280 !important;
    }

    .footer-note .print-info {
        font-size: 8pt !important;
        color: #9ca3af !important;
        font-style: italic !important;
    }

    .official-seal {
        flex: 0 0 120px !important;
        text-align: center !important;
    }

    .seal-placeholder {
        width: 100px !important;
        height: 100px !important;
        border: 3px dashed #6b7280 !important;
        border-radius: 50% !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        color: #9ca3af !important;
        font-size: 8pt !important;
        margin: 0 auto !important;
    }

    .seal-placeholder i {
        font-size: 24px !important;
        margin-bottom: 5px !important;
    }

    /* تحسينات إضافية للطباعة الرسمية */
    .print-professional .data-table {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    }

    .print-professional .official-print-header {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    .print-professional .signature-box {
        box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
    }

    /* تحسينات الخطوط للطباعة */
    .print-color,
    .print-bw,
    .print-official,
    .print-summary {
        font-family: 'Noto Sans Arabic', 'Arial', sans-serif !important;
        line-height: 1.6 !important;
    }

    .print-official h1,
    .print-official h2,
    .print-official h3 {
        font-family: 'Noto Sans Arabic', 'Arial', sans-serif !important;
        font-weight: bold !important;
    }

    /* تحسينات التباعد */
    .print-color .data-table th,
    .print-color .data-table td,
    .print-bw .data-table th,
    .print-bw .data-table td,
    .print-official .data-table th,
    .print-official .data-table td {
        padding: 10px 8px !important;
        line-height: 1.4 !important;
    }

    /* تحسينات الحدود */
    .print-color .data-table,
    .print-bw .data-table,
    .print-official .data-table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin: 20px 0 !important;
    }

    /* PDF Export Styles */
    .pdf-export {
        background: white !important;
    }

    .pdf-export .main-nav,
    .pdf-export .app-footer,
    .pdf-export .section-actions,
    .pdf-export .no-print {
        display: none !important;
    }

    .pdf-export .main-content {
        padding: 0 !important;
    }

    .pdf-export .container {
        max-width: none !important;
        padding: 2cm !important;
    }
}

/* تنسيق خاص للشاشة عند معاينة الطباعة */
@media screen {
    .print-preview {
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        margin: 20px auto;
        padding: 2cm;
        max-width: 21cm;
        min-height: 29.7cm;
    }
    
    .print-preview .print-header,
    .print-preview .print-footer {
        display: block;
    }
}
