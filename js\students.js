// إدارة الطلاب
class StudentsManager {
    constructor() {
        this.currentStudents = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStudents();
    }

    bindEvents() {
        // أحداث الفلاتر
        document.getElementById('grade-filter')?.addEventListener('change', () => this.filterStudents());
        document.getElementById('section-filter')?.addEventListener('change', () => this.filterStudents());
        document.getElementById('search-input')?.addEventListener('input', () => this.filterStudents());
    }

    async loadStudents(filters = {}) {
        try {
            this.currentStudents = await dbManager.getStudents(filters);
            this.renderStudentsTable();
            this.updateStudentCount();
        } catch (error) {
            console.error('خطأ في تحميل الطلاب:', error);
            showNotification('خطأ في تحميل بيانات الطلاب', 'error');
        }
    }

    renderStudentsTable() {
        const tbody = document.getElementById('students-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.currentStudents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد بيانات طلاب</td>
                </tr>
            `;
            return;
        }

        this.currentStudents.forEach(student => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student.student_number}</td>
                <td>${student.name}</td>
                <td>الصف ${student.grade}</td>
                <td>${student.section}</td>
                <td>${formatDate(student.registration_date)}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="studentsManager.editStudent(${student.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="studentsManager.deleteStudent(${student.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    async filterStudents() {
        const filters = {
            grade: document.getElementById('grade-filter')?.value || '',
            section: document.getElementById('section-filter')?.value || '',
            search: document.getElementById('search-input')?.value || ''
        };

        await this.loadStudents(filters);
    }

    updateStudentCount() {
        const totalElement = document.getElementById('total-students');
        if (totalElement) {
            totalElement.textContent = this.currentStudents.length;
        }
    }

    showAddStudentModal() {
        const modalContent = `
            <div class="modal-header">
                <h3>إضافة طالب جديد</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-student-form" class="student-form">
                <div class="form-group">
                    <label for="student-number">رقم الطالب:</label>
                    <input type="number" id="student-number" name="student_number" required>
                </div>
                
                <div class="form-group">
                    <label for="student-name">اسم الطالب:</label>
                    <input type="text" id="student-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="student-grade">الصف:</label>
                    <select id="student-grade" name="grade" required>
                        <option value="">اختر الصف</option>
                        <option value="1">الصف الأول</option>
                        <option value="2">الصف الثاني</option>
                        <option value="3">الصف الثالث</option>
                        <option value="4">الصف الرابع</option>
                        <option value="5">الصف الخامس</option>
                        <option value="6">الصف السادس</option>
                        <option value="7">الصف السابع</option>
                        <option value="8">الصف الثامن</option>
                        <option value="9">الصف التاسع</option>
                        <option value="10">الصف العاشر</option>
                        <option value="11">الصف الحادي عشر</option>
                        <option value="12">الصف الثاني عشر</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="student-section">الشعبة:</label>
                    <select id="student-section" name="section" required>
                        <option value="">اختر الشعبة</option>
                        <option value="أ">أ</option>
                        <option value="ب">ب</option>
                        <option value="ج">ج</option>
                        <option value="د">د</option>
                    </select>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        `;

        showModal(modalContent);
        
        document.getElementById('add-student-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveStudent();
        });
    }

    async saveStudent(studentId = null) {
        const form = document.getElementById('add-student-form');
        const formData = new FormData(form);
        
        const studentData = {
            student_number: formData.get('student_number'),
            name: formData.get('name'),
            grade: formData.get('grade'),
            section: formData.get('section')
        };

        try {
            if (studentId) {
                await dbManager.updateStudent(studentId, studentData);
                showNotification('تم تحديث بيانات الطالب بنجاح', 'success');
            } else {
                await dbManager.addStudent(studentData);
                showNotification('تم إضافة الطالب بنجاح', 'success');
            }
            
            closeModal();
            await this.loadStudents();
        } catch (error) {
            console.error('خطأ في حفظ الطالب:', error);
            showNotification('خطأ في حفظ بيانات الطالب', 'error');
        }
    }

    async editStudent(studentId) {
        try {
            const students = await dbManager.getStudents();
            const student = students.find(s => s.id === studentId);
            
            if (!student) {
                showNotification('الطالب غير موجود', 'error');
                return;
            }

            const modalContent = `
                <div class="modal-header">
                    <h3>تعديل بيانات الطالب</h3>
                    <button class="btn btn-sm btn-danger" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="add-student-form" class="student-form">
                    <div class="form-group">
                        <label for="student-number">رقم الطالب:</label>
                        <input type="number" id="student-number" name="student_number" value="${student.student_number}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="student-name">اسم الطالب:</label>
                        <input type="text" id="student-name" name="name" value="${student.name}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="student-grade">الصف:</label>
                        <select id="student-grade" name="grade" required>
                            <option value="">اختر الصف</option>
                            <option value="1" ${student.grade == 1 ? 'selected' : ''}>الصف الأول</option>
                            <option value="2" ${student.grade == 2 ? 'selected' : ''}>الصف الثاني</option>
                            <option value="3" ${student.grade == 3 ? 'selected' : ''}>الصف الثالث</option>
                            <option value="4" ${student.grade == 4 ? 'selected' : ''}>الصف الرابع</option>
                            <option value="5" ${student.grade == 5 ? 'selected' : ''}>الصف الخامس</option>
                            <option value="6" ${student.grade == 6 ? 'selected' : ''}>الصف السادس</option>
                            <option value="7" ${student.grade == 7 ? 'selected' : ''}>الصف السابع</option>
                            <option value="8" ${student.grade == 8 ? 'selected' : ''}>الصف الثامن</option>
                            <option value="9" ${student.grade == 9 ? 'selected' : ''}>الصف التاسع</option>
                            <option value="10" ${student.grade == 10 ? 'selected' : ''}>الصف العاشر</option>
                            <option value="11" ${student.grade == 11 ? 'selected' : ''}>الصف الحادي عشر</option>
                            <option value="12" ${student.grade == 12 ? 'selected' : ''}>الصف الثاني عشر</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="student-section">الشعبة:</label>
                        <select id="student-section" name="section" required>
                            <option value="">اختر الشعبة</option>
                            <option value="أ" ${student.section === 'أ' ? 'selected' : ''}>أ</option>
                            <option value="ب" ${student.section === 'ب' ? 'selected' : ''}>ب</option>
                            <option value="ج" ${student.section === 'ج' ? 'selected' : ''}>ج</option>
                            <option value="د" ${student.section === 'د' ? 'selected' : ''}>د</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            `;

            showModal(modalContent);
            
            document.getElementById('add-student-form').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveStudent(studentId);
            });

        } catch (error) {
            console.error('خطأ في تحميل بيانات الطالب:', error);
            showNotification('خطأ في تحميل بيانات الطالب', 'error');
        }
    }

    async deleteStudent(studentId) {
        if (!confirm('هل أنت متأكد من حذف هذا الطالب؟ سيتم حذف جميع درجاته أيضاً.')) {
            return;
        }

        try {
            await dbManager.deleteStudent(studentId);
            showNotification('تم حذف الطالب بنجاح', 'success');
            await this.loadStudents();
        } catch (error) {
            console.error('خطأ في حذف الطالب:', error);
            showNotification('خطأ في حذف الطالب', 'error');
        }
    }

    showImportModal() {
        const modalContent = `
            <div class="modal-header">
                <h3>استيراد الطلاب من ملف Excel</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="import-section">
                <div class="import-instructions">
                    <h4>تعليمات الاستيراد:</h4>
                    <ul>
                        <li>يجب أن يحتوي الملف على الأعمدة التالية: رقم الطالب، اسم الطالب، الصف، الشعبة</li>
                        <li>يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)</li>
                        <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                    </ul>
                </div>
                
                <div class="file-upload">
                    <input type="file" id="excel-file" accept=".xlsx,.xls" class="file-input">
                    <label for="excel-file" class="file-label">
                        <i class="fas fa-cloud-upload-alt"></i>
                        اختر ملف Excel
                    </label>
                </div>
                
                <div class="import-actions">
                    <button class="btn btn-primary" onclick="studentsManager.processExcelFile()">
                        <i class="fas fa-upload"></i> استيراد البيانات
                    </button>
                    <button class="btn btn-info" onclick="studentsManager.downloadTemplate()">
                        <i class="fas fa-download"></i> تحميل نموذج Excel
                    </button>
                </div>
            </div>
        `;

        showModal(modalContent);
    }

    downloadTemplate() {
        // إنشاء نموذج Excel للاستيراد
        const templateData = [
            ['رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة'],
            ['1001', 'أحمد محمد علي', '1', 'أ'],
            ['1002', 'فاطمة أحمد سالم', '1', 'أ'],
            ['1003', 'محمد علي حسن', '2', 'ب']
        ];

        const ws = XLSX.utils.aoa_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الطلاب');
        
        XLSX.writeFile(wb, 'نموذج_استيراد_الطلاب.xlsx');
    }

    async processExcelFile() {
        const fileInput = document.getElementById('excel-file');
        const file = fileInput.files[0];

        if (!file) {
            showNotification('يرجى اختيار ملف Excel', 'warning');
            return;
        }

        try {
            const data = await this.readExcelFile(file);
            await this.importStudentsFromExcel(data);
            closeModal();
        } catch (error) {
            console.error('خطأ في معالجة ملف Excel:', error);
            showNotification('خطأ في معالجة ملف Excel', 'error');
        }
    }

    readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    resolve(jsonData);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
        });
    }

    async importStudentsFromExcel(data) {
        if (data.length < 2) {
            showNotification('الملف فارغ أو لا يحتوي على بيانات', 'warning');
            return;
        }

        const headers = data[0];
        const rows = data.slice(1);
        let successCount = 0;
        let errorCount = 0;

        for (const row of rows) {
            if (row.length < 4) continue;

            try {
                const studentData = {
                    student_number: row[0],
                    name: row[1],
                    grade: row[2],
                    section: row[3]
                };

                await dbManager.addStudent(studentData);
                successCount++;
            } catch (error) {
                console.error('خطأ في إضافة الطالب:', error);
                errorCount++;
            }
        }

        showNotification(
            `تم استيراد ${successCount} طالب بنجاح. فشل في استيراد ${errorCount} طالب.`,
            successCount > 0 ? 'success' : 'warning'
        );

        await this.loadStudents();
    }
}

// إنشاء مثيل من مدير الطلاب
const studentsManager = new StudentsManager();

// دوال مساعدة
function showAddStudentModal() {
    studentsManager.showAddStudentModal();
}

function showImportModal() {
    studentsManager.showImportModal();
}
