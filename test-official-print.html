<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة الرسمية الاحترافية</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/print.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="print-professional">
    <div class="container">
        <h1>اختبار نظام الطباعة الرسمية الاحترافية</h1>
        
        <div class="section-actions">
            <button class="btn btn-primary official-print-btn" onclick="testOfficialPrint()">
                <i class="fas fa-stamp"></i> اختبار الطباعة الرسمية
                <div class="btn-description">
                    ✅ تصميم احترافي يليق بالمؤسسات التعليمية<br>
                    ✅ طباعة عالية الجودة مع حفظ جميع التفاصيل
                </div>
            </button>
        </div>

        <div id="test-content">
            <!-- سيتم إدراج المحتوى هنا -->
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script>
        // محاكاة كائن app
        const app = {
            settings: {
                school_name: 'مدرسة الملك عبدالعزيز الثانوية',
                current_academic_year: '2024-2025',
                director_name: 'أحمد محمد العلي',
                supervisor_name: 'فاطمة أحمد السعد',
                teacher_name: 'محمد سعد الخالدي'
            }
        };

        function testOfficialPrint() {
            const container = document.getElementById('test-content');
            
            // إنشاء الرأس الرسمي الاحترافي
            const officialHeader = document.createElement('div');
            officialHeader.className = 'official-print-header';
            officialHeader.innerHTML = `
                <div class="ministry-header">
                    <div class="ministry-logo">
                        <div class="logo-circle">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </div>
                    <div class="ministry-info">
                        <h1 class="ministry-name">المملكة العربية السعودية</h1>
                        <h2 class="ministry-dept">وزارة التربية والتعليم</h2>
                        <h3 class="school-name">${app.settings.school_name}</h3>
                    </div>
                    <div class="document-info">
                        <div class="doc-date">التاريخ: ${formatDateArabic(new Date())}</div>
                        <div class="doc-time">الوقت: ${formatTime(new Date())}</div>
                        <div class="academic-year">العام الدراسي: ${app.settings.current_academic_year}</div>
                    </div>
                </div>
                
                <div class="document-title">
                    <h2 class="main-title">تقرير تقويم تقنية المعلومات</h2>
                    <div class="title-decoration"></div>
                    <p class="document-subtitle">تقرير رسمي شامل لأداء الطلاب في مادة تقنية المعلومات</p>
                </div>
            `;

            // إنشاء جدول تجريبي
            const sampleTable = document.createElement('div');
            sampleTable.innerHTML = `
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>رقم الطالب</th>
                                <th>اسم الطالب</th>
                                <th>الصف</th>
                                <th>الشعبة</th>
                                <th>الفصل الأول</th>
                                <th>الفصل الثاني</th>
                                <th>المجموع</th>
                                <th>النسبة</th>
                                <th>التقدير</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="grade-excellent">
                                <td class="rank-cell">1</td>
                                <td>2024001</td>
                                <td class="student-info">أحمد محمد علي</td>
                                <td>الثالث الثانوي</td>
                                <td>أ</td>
                                <td>48</td>
                                <td>47</td>
                                <td class="final-total">95</td>
                                <td class="percentage-cell">95%</td>
                                <td class="grade-cell">ممتاز</td>
                            </tr>
                            <tr class="grade-very-good">
                                <td class="rank-cell">2</td>
                                <td>2024002</td>
                                <td class="student-info">فاطمة أحمد السعد</td>
                                <td>الثالث الثانوي</td>
                                <td>أ</td>
                                <td>42</td>
                                <td>43</td>
                                <td class="final-total">85</td>
                                <td class="percentage-cell">85%</td>
                                <td class="grade-cell">جيد جداً</td>
                            </tr>
                            <tr class="grade-good">
                                <td class="rank-cell">3</td>
                                <td>2024003</td>
                                <td class="student-info">محمد سعد الخالدي</td>
                                <td>الثالث الثانوي</td>
                                <td>ب</td>
                                <td>38</td>
                                <td>37</td>
                                <td class="final-total">75</td>
                                <td class="percentage-cell">75%</td>
                                <td class="grade-cell">جيد</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;

            // إنشاء التذييل الرسمي المضغوط
            const officialFooter = document.createElement('div');
            officialFooter.className = 'official-print-footer';
            officialFooter.innerHTML = `
                <div class="signatures-section-compact">
                    <div class="signatures-inline">
                        <div class="signature-inline">
                            <span class="sig-title">مدير المدرسة:</span>
                            <span class="sig-name">${app.settings.director_name}</span>
                            <span class="sig-line">التوقيع: ________________</span>
                        </div>
                        <div class="signature-inline">
                            <span class="sig-title">المشرف التربوي:</span>
                            <span class="sig-name">${app.settings.supervisor_name}</span>
                            <span class="sig-line">التوقيع: ________________</span>
                        </div>
                        <div class="signature-inline">
                            <span class="sig-title">معلم المادة:</span>
                            <span class="sig-name">${app.settings.teacher_name}</span>
                            <span class="sig-line">التوقيع: ________________</span>
                        </div>
                    </div>
                </div>
                
                <div class="document-footer">
                    <div class="footer-note">
                        <p><strong>ملاحظة:</strong> هذا التقرير معتمد رسمياً من نظام تقويم تقنية المعلومات</p>
                        <p class="print-info">طُبع في: ${formatDateArabic(new Date())} - ${formatTime(new Date())}</p>
                    </div>
                    <div class="official-seal">
                        <div class="seal-placeholder">
                            <i class="fas fa-stamp"></i>
                            <span>الختم الرسمي</span>
                        </div>
                    </div>
                </div>
            `;

            // تجميع المحتوى
            container.innerHTML = '';
            container.appendChild(officialHeader);
            container.appendChild(sampleTable);
            container.appendChild(officialFooter);

            // إضافة فئات الطباعة
            document.body.classList.add('print-official', 'print-professional');

            // طباعة بعد تأخير قصير
            setTimeout(() => {
                window.print();
            }, 500);
        }
    </script>
</body>
</html>
