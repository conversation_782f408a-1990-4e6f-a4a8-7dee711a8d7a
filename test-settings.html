<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات الجديدة</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/print.css">
</head>
<body>
    <div class="container">
        <h1>اختبار الإعدادات الجديدة</h1>
        
        <div class="settings-grid">
            <div class="setting-group">
                <h3>إعدادات النظام</h3>
                <div class="setting-item">
                    <label>العام الدراسي الحالي:</label>
                    <input type="text" id="current-academic-year" value="2024-2025">
                </div>
                <div class="setting-item">
                    <label>اسم المدرسة:</label>
                    <input type="text" id="school-name" placeholder="اسم المدرسة" value="مدرسة تقنية المعلومات">
                </div>
            </div>
            
            <div class="setting-group">
                <h3>أسماء المسؤولين</h3>
                <div class="setting-item">
                    <label>اسم المدير:</label>
                    <input type="text" id="director-name" placeholder="اسم مدير المدرسة" value="أحمد محمد علي">
                </div>
                <div class="setting-item">
                    <label>اسم المشرف:</label>
                    <input type="text" id="supervisor-name" placeholder="اسم المشرف" value="فاطمة أحمد">
                </div>
                <div class="setting-item">
                    <label>اسم المعلم:</label>
                    <input type="text" id="teacher-name" placeholder="اسم المعلم المسؤول" value="محمد سعد">
                </div>
            </div>
        </div>

        <div class="section-actions">
            <button class="btn btn-primary" onclick="testPrintMetadata()">اختبار نموذج الطباعة</button>
        </div>

        <div id="test-content" style="margin-top: 2rem;">
            <!-- سيتم إدراج نموذج الطباعة هنا -->
        </div>
    </div>

    <script>
        // محاكاة كائن app
        const app = {
            settings: {
                school_name: 'مدرسة تقنية المعلومات',
                director_name: 'أحمد محمد علي',
                supervisor_name: 'فاطمة أحمد',
                teacher_name: 'محمد سعد'
            }
        };

        // دوال مساعدة
        function formatDateArabic(date) {
            return date.toLocaleDateString('ar-SA');
        }

        function formatTime(date) {
            return date.toLocaleTimeString('ar-SA');
        }

        function testPrintMetadata() {
            const container = document.getElementById('test-content');
            
            // الحصول على أسماء المسؤولين من الحقول
            const directorName = document.getElementById('director-name').value || 'غير محدد';
            const supervisorName = document.getElementById('supervisor-name').value || 'غير محدد';
            const teacherName = document.getElementById('teacher-name').value || 'غير محدد';
            const schoolName = document.getElementById('school-name').value || 'مدرسة تقنية المعلومات';

            const metadata = document.createElement('div');
            metadata.className = 'print-metadata';
            metadata.innerHTML = `
                <div class="print-info-header">
                    <div class="print-timestamp">تاريخ الطباعة: ${formatDateArabic(new Date())} - ${formatTime(new Date())}</div>
                    <div class="print-type">نوع الطباعة: اختبار</div>
                    <div class="print-user">طُبع بواسطة: نظام تقويم تقنية المعلومات</div>
                </div>
                <div class="print-officials">
                    <div class="print-school-info">
                        <strong>${schoolName}</strong>
                    </div>
                    <div class="print-names-row">
                        <div class="print-official">
                            <span class="official-title">المدير:</span>
                            <span class="official-name">${directorName}</span>
                            <span class="signature-line">التوقيع: ________________</span>
                        </div>
                        <div class="print-official">
                            <span class="official-title">المشرف:</span>
                            <span class="official-name">${supervisorName}</span>
                            <span class="signature-line">التوقيع: ________________</span>
                        </div>
                        <div class="print-official">
                            <span class="official-title">المعلم:</span>
                            <span class="official-name">${teacherName}</span>
                            <span class="signature-line">التوقيع: ________________</span>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = '';
            container.appendChild(metadata);
        }
    </script>
</body>
</html>
