// إدارة النتائج النهائية
class FinalResultsManager {
    constructor() {
        this.currentResults = [];
        this.gradeStructures = this.initGradeStructures();
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // أحداث الفلاتر
        document.getElementById('final-academic-year')?.addEventListener('change', () => this.clearResults());
        document.getElementById('final-grade-filter')?.addEventListener('change', () => this.clearResults());
        document.getElementById('final-section-filter')?.addEventListener('change', () => this.clearResults());
    }

    initGradeStructures() {
        return {
            elementary: {
                grades: [1, 2, 3, 4],
                maxTotal: 100, // 50 × 2 فصول
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز', percentage: 90 },
                    { min: 80, level: 'ب', description: 'جيد جداً', percentage: 80 },
                    { min: 70, level: 'ج', description: 'جيد', percentage: 70 },
                    { min: 60, level: 'د', description: 'مقبول', percentage: 60 },
                    { min: 0, level: 'هـ', description: 'راسب', percentage: 0 }
                ]
            },
            intermediate: {
                grades: [5, 6, 7, 8, 9, 10],
                maxTotal: 200, // 100 × 2 فصول
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز', percentage: 90 },
                    { min: 80, level: 'ب', description: 'جيد جداً', percentage: 80 },
                    { min: 70, level: 'ج', description: 'جيد', percentage: 70 },
                    { min: 60, level: 'د', description: 'مقبول', percentage: 60 },
                    { min: 0, level: 'هـ', description: 'راسب', percentage: 0 }
                ]
            },
            secondary: {
                grades: [11, 12],
                maxTotal: 200, // 100 × 2 فصول
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز', percentage: 90 },
                    { min: 80, level: 'ب', description: 'جيد جداً', percentage: 80 },
                    { min: 70, level: 'ج', description: 'جيد', percentage: 70 },
                    { min: 60, level: 'د', description: 'مقبول', percentage: 60 },
                    { min: 0, level: 'هـ', description: 'راسب', percentage: 0 }
                ]
            }
        };
    }

    clearResults() {
        const container = document.getElementById('final-results-container');
        if (container) {
            container.innerHTML = `
                <div class="no-results-message">
                    <i class="fas fa-info-circle"></i>
                    <p>اختر المعايير أعلاه لعرض النتائج النهائية</p>
                </div>
            `;
        }
    }

    async loadFinalResults() {
        try {
            const academicYear = document.getElementById('final-academic-year').value;
            const grade = document.getElementById('final-grade-filter').value;
            const section = document.getElementById('final-section-filter').value;

            if (!academicYear) {
                showNotification('يرجى اختيار العام الدراسي', 'warning');
                return;
            }

            // تحميل الطلاب
            const studentFilters = {};
            if (grade) studentFilters.grade = grade;
            if (section) studentFilters.section = section;

            const students = await dbManager.getStudents(studentFilters);
            
            if (students.length === 0) {
                this.showNoStudentsMessage();
                return;
            }

            // تحميل درجات الفصل الأول والثاني
            const semester1Grades = await dbManager.getGrades({
                academic_year: academicYear,
                semester: 1,
                grade_level: grade
            });

            const semester2Grades = await dbManager.getGrades({
                academic_year: academicYear,
                semester: 2,
                grade_level: grade
            });

            // حساب النتائج النهائية
            const finalResults = this.calculateFinalResults(students, semester1Grades, semester2Grades);
            
            // عرض النتائج
            this.renderFinalResults(finalResults, academicYear, grade, section);

        } catch (error) {
            console.error('خطأ في تحميل النتائج النهائية:', error);
            showNotification('خطأ في تحميل النتائج النهائية', 'error');
        }
    }

    calculateFinalResults(students, semester1Grades, semester2Grades) {
        const results = [];

        students.forEach(student => {
            const s1Grade = semester1Grades.find(g => g.student_id === student.id);
            const s2Grade = semester2Grades.find(g => g.student_id === student.id);

            const semester1Total = s1Grade ? s1Grade.total : 0;
            const semester2Total = s2Grade ? s2Grade.total : 0;
            const finalTotal = semester1Total + semester2Total;
            const average = finalTotal / 2;

            // تحديد هيكل الدرجات حسب الصف
            let structure;
            if (student.grade >= 1 && student.grade <= 4) {
                structure = this.gradeStructures.elementary;
            } else if (student.grade >= 5 && student.grade <= 10) {
                structure = this.gradeStructures.intermediate;
            } else {
                structure = this.gradeStructures.secondary;
            }

            const percentage = (finalTotal / structure.maxTotal) * 100;
            const gradeInfo = this.getGradeInfo(percentage, structure);

            results.push({
                student: student,
                semester1Total: semester1Total,
                semester2Total: semester2Total,
                finalTotal: finalTotal,
                average: average,
                percentage: percentage,
                grade: gradeInfo.level,
                description: gradeInfo.description,
                maxTotal: structure.maxTotal,
                hasBothSemesters: s1Grade && s2Grade
            });
        });

        // ترتيب النتائج حسب المجموع الكلي (تنازلي)
        results.sort((a, b) => {
            // ترتيب أولاً حسب المجموع الكلي
            if (b.finalTotal !== a.finalTotal) {
                return b.finalTotal - a.finalTotal;
            }
            // في حالة التساوي، ترتيب حسب النسبة المئوية
            if (b.percentage !== a.percentage) {
                return b.percentage - a.percentage;
            }
            // في حالة التساوي الكامل، ترتيب أبجدياً حسب الاسم
            return a.student.name.localeCompare(b.student.name, 'ar');
        });

        // إضافة رقم الترتيب
        results.forEach((result, index) => {
            result.rank = index + 1;
        });

        this.currentResults = results;
        return results;
    }

    getGradeInfo(percentage, structure) {
        for (const grade of structure.gradeScale) {
            if (percentage >= grade.percentage) {
                return grade;
            }
        }
        return structure.gradeScale[structure.gradeScale.length - 1];
    }

    showNoStudentsMessage() {
        const container = document.getElementById('final-results-container');
        container.innerHTML = `
            <div class="no-results-message">
                <i class="fas fa-exclamation-triangle"></i>
                <p>لا توجد طلاب مطابقون للمعايير المحددة</p>
            </div>
        `;
    }

    renderFinalResults(results, academicYear, grade, section) {
        const container = document.getElementById('final-results-container');
        
        // إنشاء رأس النتائج
        let headerInfo = `العام الدراسي: ${academicYear}`;
        if (grade) headerInfo += ` - الصف: ${grade}`;
        if (section) headerInfo += ` - الشعبة: ${section}`;

        let html = `
            <div class="final-results-header">
                <h3>النتائج النهائية</h3>
                <div class="final-results-info">
                    <span>${headerInfo}</span>
                    <span>عدد الطلاب: ${results.length}</span>
                </div>
            </div>
            
            <div class="final-results-table-container">
                <table class="final-results-table">
                    <thead>
                        <tr>
                            <th rowspan="2">الترتيب</th>
                            <th rowspan="2">رقم الطالب</th>
                            <th rowspan="2">اسم الطالب</th>
                            <th rowspan="2">الصف</th>
                            <th rowspan="2">الشعبة</th>
                            <th colspan="2">درجات الفصول</th>
                            <th rowspan="2">المجموع الكلي</th>
                            <th rowspan="2">متوسط الفصلين</th>
                            <th rowspan="2">النسبة المئوية</th>
                            <th rowspan="2">التقدير</th>
                            <th rowspan="2">المستوى</th>
                        </tr>
                        <tr>
                            <th>الفصل الأول</th>
                            <th>الفصل الثاني</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف الطلاب
        results.forEach((result, index) => {
            const gradeClass = this.getGradeCSSClass(result.grade);
            const warningClass = !result.hasBothSemesters ? 'incomplete-data' : '';
            
            html += `
                <tr class="${gradeClass} ${warningClass}">
                    <td class="rank-cell">${result.rank}</td>
                    <td>${result.student.student_number}</td>
                    <td class="student-info">${result.student.name}</td>
                    <td>${result.student.grade}</td>
                    <td>${result.student.section}</td>
                    <td class="semester-total">${result.semester1Total.toFixed(2)}</td>
                    <td class="semester-total">${result.semester2Total.toFixed(2)}</td>
                    <td class="final-total">${result.finalTotal.toFixed(2)}</td>
                    <td class="average-cell">${result.average.toFixed(2)}</td>
                    <td class="percentage-cell">${result.percentage.toFixed(1)}%</td>
                    <td class="grade-cell">${result.grade}</td>
                    <td>${result.description}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        // إضافة ملخص الإحصائيات
        html += this.generateSummaryStats(results);

        // إضافة رسالة تحذيرية للبيانات غير المكتملة
        const incompleteCount = results.filter(r => !r.hasBothSemesters).length;
        if (incompleteCount > 0) {
            html += `
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> يوجد ${incompleteCount} طالب/طالبة لديهم بيانات غير مكتملة (درجات فصل واحد فقط).
                    يرجى التأكد من إدخال درجات الفصلين للحصول على نتائج دقيقة.
                </div>
            `;
        }

        container.innerHTML = html;
        showNotification('تم تحميل النتائج النهائية بنجاح', 'success');
    }

    generateSummaryStats(results) {
        const totalStudents = results.length;
        const studentsWithBothSemesters = results.filter(r => r.hasBothSemesters).length;
        const averageTotal = results.reduce((sum, r) => sum + r.finalTotal, 0) / totalStudents;
        const averagePercentage = results.reduce((sum, r) => sum + r.percentage, 0) / totalStudents;

        // توزيع الدرجات
        const gradeDistribution = {
            'أ': results.filter(r => r.grade === 'أ').length,
            'ب': results.filter(r => r.grade === 'ب').length,
            'ج': results.filter(r => r.grade === 'ج').length,
            'د': results.filter(r => r.grade === 'د').length,
            'هـ': results.filter(r => r.grade === 'هـ').length
        };

        const passCount = results.filter(r => r.grade !== 'هـ').length;
        const passRate = (passCount / totalStudents) * 100;

        return `
            <div class="final-results-summary">
                <div class="summary-stats">
                    <div class="summary-stat">
                        <h4>إجمالي الطلاب</h4>
                        <div class="stat-value">${totalStudents}</div>
                    </div>
                    <div class="summary-stat">
                        <h4>الطلاب مكتملو البيانات</h4>
                        <div class="stat-value">${studentsWithBothSemesters}</div>
                    </div>
                    <div class="summary-stat">
                        <h4>متوسط المجموع الكلي</h4>
                        <div class="stat-value">${averageTotal.toFixed(2)}</div>
                    </div>
                    <div class="summary-stat">
                        <h4>متوسط النسبة المئوية</h4>
                        <div class="stat-value">${averagePercentage.toFixed(1)}%</div>
                    </div>
                    <div class="summary-stat">
                        <h4>نسبة النجاح</h4>
                        <div class="stat-value">${passRate.toFixed(1)}%</div>
                    </div>
                </div>
                
                <h4 style="text-align: center; margin: 1rem 0; color: var(--primary-color);">توزيع التقديرات</h4>
                <div class="grade-distribution-summary">
                    <div class="grade-summary-item grade-summary-a">
                        <div>أ (ممتاز)</div>
                        <div>${gradeDistribution['أ']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-b">
                        <div>ب (جيد جداً)</div>
                        <div>${gradeDistribution['ب']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-c">
                        <div>ج (جيد)</div>
                        <div>${gradeDistribution['ج']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-d">
                        <div>د (مقبول)</div>
                        <div>${gradeDistribution['د']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-f">
                        <div>هـ (راسب)</div>
                        <div>${gradeDistribution['هـ']}</div>
                    </div>
                </div>
            </div>
        `;
    }

    getGradeCSSClass(grade) {
        const gradeClasses = {
            'أ': 'final-grade-a',
            'ب': 'final-grade-b',
            'ج': 'final-grade-c',
            'د': 'final-grade-d',
            'هـ': 'final-grade-f'
        };
        return gradeClasses[grade] || '';
    }

    exportToExcel() {
        if (this.currentResults.length === 0) {
            showNotification('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        try {
            // إنشاء البيانات للتصدير
            const exportData = [
                ['الترتيب', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'الفصل الأول', 'الفصل الثاني', 'المجموع الكلي', 'متوسط الفصلين', 'النسبة المئوية', 'التقدير', 'المستوى']
            ];

            this.currentResults.forEach((result) => {
                exportData.push([
                    result.rank,
                    result.student.student_number,
                    result.student.name,
                    result.student.grade,
                    result.student.section,
                    result.semester1Total.toFixed(2),
                    result.semester2Total.toFixed(2),
                    result.finalTotal.toFixed(2),
                    result.average.toFixed(2),
                    result.percentage.toFixed(1) + '%',
                    result.grade,
                    result.description
                ]);
            });

            // إنشاء ملف Excel
            const ws = XLSX.utils.aoa_to_sheet(exportData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'النتائج النهائية');

            // تنسيق الجدول
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = range.s.r; R <= range.e.r; ++R) {
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const cell_address = XLSX.utils.encode_cell({ c: C, r: R });
                    if (!ws[cell_address]) continue;
                    
                    // تنسيق الرأس
                    if (R === 0) {
                        ws[cell_address].s = {
                            font: { bold: true },
                            fill: { fgColor: { rgb: "2c3e50" } },
                            alignment: { horizontal: "center" }
                        };
                    }
                }
            }

            const fileName = `النتائج_النهائية_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
            
            showNotification('تم تصدير النتائج النهائية بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير النتائج:', error);
            showNotification('خطأ في تصدير النتائج', 'error');
        }
    }

    printResults(type = 'official') {
        if (this.currentResults.length === 0) {
            showNotification('لا توجد نتائج للطباعة', 'warning');
            return;
        }

        const body = document.body;

        // إزالة جميع فئات الطباعة السابقة
        body.classList.remove('print-color', 'print-bw', 'print-official', 'print-summary');

        // تطبيق النمط الرسمي الاحترافي كافتراضي
        body.classList.add('printing-final-results', 'print-official', 'print-professional');

        // إضافة معلومات الطباعة الرسمية
        this.addOfficialPrintMetadata();

        // طباعة النتائج
        setTimeout(() => {
            window.print();

            // إزالة فئات الطباعة بعد الطباعة
            setTimeout(() => {
                body.classList.remove('printing-final-results', 'print-official', 'print-professional');
                this.removePrintMetadata();
            }, 1000);
        }, 500);

        showNotification('تم إعداد النتائج النهائية للطباعة الرسمية الاحترافية', 'success');
    }

    getPrintTypeName(type) {
        const names = {
            'color': 'الملونة',
            'bw': 'بالأبيض والأسود',
            'official': 'الرسمية',
            'summary': 'الملخص التنفيذي'
        };
        return names[type] || 'العادية';
    }

    addOfficialPrintMetadata() {
        const container = document.getElementById('final-results-container');
        if (!container) return;

        // الحصول على أسماء المسؤولين من الإعدادات
        const directorName = app.settings.director_name || 'غير محدد';
        const supervisorName = app.settings.supervisor_name || 'غير محدد';
        const teacherName = app.settings.teacher_name || 'غير محدد';
        const schoolName = app.settings.school_name || 'مدرسة تقنية المعلومات';
        const currentYear = app.settings.current_academic_year || '2024-2025';

        // إنشاء الرأس الرسمي الاحترافي
        const officialHeader = document.createElement('div');
        officialHeader.className = 'official-print-header';
        officialHeader.innerHTML = `
            <div class="ministry-header">
                <div class="ministry-logo">
                    <div class="logo-circle">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                </div>
                <div class="ministry-info">
                    <h1 class="ministry-name">المملكة العربية السعودية</h1>
                    <h2 class="ministry-dept">وزارة التربية والتعليم</h2>
                    <h3 class="school-name">${schoolName}</h3>
                </div>
                <div class="document-info">
                    <div class="doc-date">التاريخ: ${formatDateArabic(new Date())}</div>
                    <div class="doc-time">الوقت: ${formatTime(new Date())}</div>
                    <div class="academic-year">العام الدراسي: ${currentYear}</div>
                </div>
            </div>

            <div class="document-title">
                <h2 class="main-title">النتائج النهائية - تقنية المعلومات</h2>
                <div class="title-decoration"></div>
                <p class="document-subtitle">كشف النتائج النهائية الرسمي لطلاب مادة تقنية المعلومات</p>
            </div>
        `;

        // إنشاء التذييل الرسمي
        const officialFooter = document.createElement('div');
        officialFooter.className = 'official-print-footer';
        officialFooter.innerHTML = `
            <div class="signatures-section">
                <div class="signature-row">
                    <div class="signature-box">
                        <div class="signature-title">مدير المدرسة</div>
                        <div class="signature-name">${directorName}</div>
                        <div class="signature-line"></div>
                        <div class="signature-label">التوقيع والختم</div>
                        <div class="signature-date">التاريخ: ___________</div>
                    </div>

                    <div class="signature-box">
                        <div class="signature-title">المشرف التربوي</div>
                        <div class="signature-name">${supervisorName}</div>
                        <div class="signature-line"></div>
                        <div class="signature-label">التوقيع والختم</div>
                        <div class="signature-date">التاريخ: ___________</div>
                    </div>

                    <div class="signature-box">
                        <div class="signature-title">معلم المادة</div>
                        <div class="signature-name">${teacherName}</div>
                        <div class="signature-line"></div>
                        <div class="signature-label">التوقيع</div>
                        <div class="signature-date">التاريخ: ___________</div>
                    </div>
                </div>
            </div>

            <div class="document-footer">
                <div class="footer-note">
                    <p><strong>ملاحظة:</strong> هذا الكشف معتمد رسمياً من نظام تقويم تقنية المعلومات</p>
                    <p class="print-info">طُبع في: ${formatDateArabic(new Date())} - ${formatTime(new Date())}</p>
                </div>
                <div class="official-seal">
                    <div class="seal-placeholder">
                        <i class="fas fa-stamp"></i>
                        <span>الختم الرسمي</span>
                    </div>
                </div>
            </div>
        `;

        // إدراج الرأس والتذييل
        container.insertBefore(officialHeader, container.firstChild);
        container.appendChild(officialFooter);
    }

    removePrintMetadata() {
        const metadata = document.querySelector('.print-metadata');
        if (metadata) {
            metadata.remove();
        }
    }
}

// إنشاء مثيل من مدير النتائج النهائية
const finalResultsManager = new FinalResultsManager();
