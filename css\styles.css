/* استمارة تقويم تقنية المعلومات - الأنماط الرئيسية */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f6fa;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-section i {
    font-size: 2rem;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
    font-size: 0.9rem;
}

/* Navigation */
.main-nav {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
}

.nav-menu li {
    flex: 1;
}

.nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--light-color);
    border-bottom-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Dashboard */
.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.card:nth-child(1) .card-icon { background: var(--primary-color); }
.card:nth-child(2) .card-icon { background: var(--success-color); }
.card:nth-child(3) .card-icon { background: var(--warning-color); }
.card:nth-child(4) .card-icon { background: var(--info-color); }

.card-content h3 {
    font-size: 1rem;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.card-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.quick-actions h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1a252f;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #2980b9;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #219a52;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    font-size: 2rem;
    color: var(--primary-color);
}

.section-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.filter-group select,
.filter-group input {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
}

/* Tables */
.students-table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--primary-color);
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* Grade Selection */
.grade-selection {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.selection-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.selection-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.selection-group select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
}

/* Grade Sheet */
.grade-sheet-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-top: 1.5rem;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
}

.report-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.report-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-group {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.setting-group h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.setting-item input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
}

/* Footer */
.app-footer {
    background: var(--primary-color);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}

/* Modal */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .nav-menu {
        flex-direction: column;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .filters-section,
    .grade-selection {
        flex-direction: column;
        align-items: stretch;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .section-actions {
        justify-content: center;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-section.active {
    animation: fadeIn 0.3s ease;
}

/* Forms */
.student-form,
.criteria-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 500px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

/* Import Section */
.import-section {
    padding: 1.5rem;
}

.import-instructions {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.import-instructions h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.import-instructions ul {
    margin: 0;
    padding-right: 1.5rem;
}

.import-instructions li {
    margin-bottom: 0.5rem;
}

.file-upload {
    text-align: center;
    margin: 1.5rem 0;
}

.file-input {
    display: none;
}

.file-label {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
    font-weight: 500;
}

.file-label:hover {
    background: #2980b9;
}

.import-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Grade Sheet Styles */
.grade-sheet-header {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 2px solid var(--border-color);
}

.grade-sheet-header h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.grade-sheet-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.grade-table-container {
    overflow-x: auto;
    background: white;
}

.grade-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.grade-table th,
.grade-table td {
    padding: 8px 6px;
    text-align: center;
    border: 1px solid var(--border-color);
    vertical-align: middle;
}

.grade-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.grade-table tbody tr:hover {
    background: #f8f9fa;
}

.grade-input {
    width: 70px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 0.85rem;
}

.grade-input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 3px rgba(52, 152, 219, 0.3);
}

.total-cell,
.level-cell,
.description-cell {
    font-weight: bold;
    background: #f8f9fa;
}

.grade-sheet-actions {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    display: flex;
    gap: 1rem;
    justify-content: center;
    border-top: 1px solid var(--border-color);
}

/* Report Content */
.report-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-top: 1.5rem;
    overflow: hidden;
}

.statistics-section,
.grade-distribution-section,
.detailed-grades-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.statistics-section:last-child,
.grade-distribution-section:last-child,
.detailed-grades-section:last-child {
    border-bottom: none;
}

.statistics-section h3,
.grade-distribution-section h3,
.detailed-grades-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

/* Button Sizes */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.125rem;
}

/* Final Results */
.final-results-filters {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.final-results-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.no-results-message {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-results-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--info-color);
}

.final-results-header {
    background: var(--primary-color);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.final-results-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.final-results-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.final-results-table-container {
    overflow-x: auto;
    max-height: 70vh;
}

.final-results-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.final-results-table th,
.final-results-table td {
    padding: 10px 8px;
    text-align: center;
    border: 1px solid var(--border-color);
    vertical-align: middle;
}

.final-results-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.final-results-table tbody tr:hover {
    background: #f8f9fa;
}

.final-results-table .student-info {
    text-align: right;
    font-weight: 500;
}

.final-results-table .semester-total {
    font-weight: bold;
    background: #f8f9fa;
}

.final-results-table .final-total {
    font-weight: bold;
    background: var(--primary-color);
    color: white;
}

.final-results-table .average-cell {
    font-weight: bold;
    background: #e3f2fd;
}

.final-results-table .percentage-cell {
    font-weight: bold;
}

.final-results-table .grade-cell {
    font-weight: bold;
    font-size: 1rem;
}

/* Final Results Grade Colors */
.final-grade-a { background: #c8e6c9 !important; color: #2e7d32; }
.final-grade-b { background: #bbdefb !important; color: #1565c0; }
.final-grade-c { background: #fff3e0 !important; color: #ef6c00; }
.final-grade-d { background: #ffecb3 !important; color: #f57f17; }
.final-grade-f { background: #ffcdd2 !important; color: #c62828; }

.final-results-summary {
    padding: 1.5rem;
    border-top: 2px solid var(--border-color);
    background: #f8f9fa;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.summary-stat {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-stat h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.summary-stat .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.grade-distribution-summary {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.grade-summary-item {
    text-align: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: bold;
    min-width: 80px;
}

.grade-summary-a { background: #c8e6c9; color: #2e7d32; }
.grade-summary-b { background: #bbdefb; color: #1565c0; }
.grade-summary-c { background: #fff3e0; color: #ef6c00; }
.grade-summary-d { background: #ffecb3; color: #f57f17; }
.grade-summary-f { background: #ffcdd2; color: #c62828; }

/* البيانات غير المكتملة */
.incomplete-data {
    background: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
}

.incomplete-data::after {
    content: " ⚠️";
    color: #ffc107;
    font-weight: bold;
}

/* تحسينات إضافية للنتائج النهائية */
.rank-cell {
    font-weight: bold;
    background: #e3f2fd !important;
    color: #1565c0;
}

.student-info {
    text-align: right !important;
    font-weight: 500;
    padding-right: 10px !important;
}

/* رسائل التحذير */
.warning-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    color: #856404;
}

.warning-message i {
    margin-left: 0.5rem;
    color: #ffc107;
}

/* Report Actions - أزرار التقارير المحسنة */
.report-actions {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: var(--shadow);
}

.print-options-section,
.export-options-section,
.preview-options-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.print-options-section:last-child,
.export-options-section:last-child,
.preview-options-section:last-child {
    margin-bottom: 0;
}

.print-options-section h4,
.export-options-section h4,
.preview-options-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.print-buttons-grid,
.export-buttons-grid,
.preview-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.print-btn {
    padding: 12px 20px;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 50px;
}

.print-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.print-btn i {
    font-size: 1.1rem;
}

/* Print Settings Modal */
.print-settings-content {
    max-width: 600px;
    padding: 1.5rem;
}

.setting-group {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: #f8f9fa;
}

.setting-group h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.setting-item {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.setting-item input[type="radio"],
.setting-item input[type="checkbox"] {
    margin-left: 0.5rem;
}

.setting-item select {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
}

/* Print Mode Styles */
.print-mode {
    background: white !important;
}

.print-mode .main-nav,
.print-mode .app-footer,
.print-mode .section-actions,
.print-mode .filters-section,
.print-mode .no-print {
    display: none !important;
}

.print-mode .main-content {
    padding: 0 !important;
}

.print-mode .container {
    max-width: none !important;
    padding: 2cm !important;
}

/* Print Metadata */
.print-metadata {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
}

.print-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 1rem;
}

/* تنسيق أسماء المسؤولين */
.print-officials {
    margin-top: 1rem;
}

.print-school-info {
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
}

.print-names-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
    margin-top: 1rem;
}

.print-official {
    flex: 1;
    text-align: center;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.8);
}

.official-title {
    display: block;
    font-weight: bold;
    font-size: 0.9rem;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.official-name {
    display: block;
    font-size: 1rem;
    color: var(--secondary-color);
    font-weight: bold;
    margin-bottom: 0.5rem;
    min-height: 1.5rem;
}

.signature-line {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
}

/* أنماط أزرار الطباعة الرسمية */
.official-print-btn {
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a) !important;
    border: none !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 120px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
}

.official-print-btn:hover {
    background: linear-gradient(135deg, #1e3a8a, #1e40af) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(44, 90, 160, 0.4) !important;
}

.official-print-btn i {
    font-size: 24px !important;
    margin-bottom: 10px !important;
}

.btn-description {
    font-size: 0.75rem !important;
    line-height: 1.4 !important;
    margin-top: 8px !important;
    opacity: 0.9 !important;
    text-align: center !important;
}

.primary-print {
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.primary-print:hover {
    background: linear-gradient(135deg, #1e3a8a, #1e40af) !important;
    transform: translateY(-1px) !important;
}

.print-desc {
    display: block !important;
    font-size: 0.7rem !important;
    opacity: 0.8 !important;
    margin-top: 4px !important;
}

/* Enhanced Button Styles */
.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-outline-info {
    background: transparent;
    color: var(--info-color);
    border: 2px solid var(--info-color);
}

.btn-outline-info:hover {
    background: var(--info-color);
    color: white;
}

/* Dropdown Styles */
.dropdown-group {
    position: relative;
    display: inline-block;
}

.dropdown-toggle::after {
    content: " ▼";
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 200px;
    padding: 0.5rem 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    text-align: right;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Responsive Design for Report Actions */
@media (max-width: 768px) {
    .print-buttons-grid,
    .export-buttons-grid,
    .preview-buttons-grid {
        grid-template-columns: 1fr;
    }

    .print-info-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-actions {
        padding: 1rem;
    }

    .print-options-section,
    .export-options-section,
    .preview-options-section {
        padding: 1rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.hidden { display: none; }
.visible { display: block; }
